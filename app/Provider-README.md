**Template Provider** 

**Read one**

**Description** : Gets the Template Details for a requested Template.

**Query** :

Id : <GloablId: LocalId {ex. 9be68ea1-be88-4c03-b91d-ba19e20eb386 in this case}> 

Sample Query: 

`query q {
  node(id: "djQuMTo6YjgwMmNiMWRlMw:9be68ea1-be88-4c03-b91d-ba19e20eb386") {
    ... on Workflows_Template {
      name
      id
      version
      workflowSteps(filterBy: "id=null") {
        edges {
          node {
            id
            trigger {
              id
              parameters {
                parameterType
                getOptionsForFieldValue
                parameterName
                configurable
                required
                multiSelect
              }
            }
            workflowStepCondition {
              id
              conditionalInputParameters {
                inputParameter {
                  parameterType
                  getOptionsForFieldValue
                  parameterName
                  configurable
                  required
                  multiSelect
                }
                supportedOperators {
                  symbol
                  description
                }
              }
            }
            actions {
              actionKey
              action {
                id
                nexts {
                  nextType
                  nextAction {
                    id
                  }
                  nextWorkflowStep {
                    id
                  }
                }
                parameters {
                  parameterType
                  getOptionsForFieldValue
                  parameterName
                  configurable
                  required
                  multiSelect
                }
              }
            }
          }
        }
      }
    }
  }
}`


**Read List  Description : Gets the List of Templates available**

**Sample Query :**

````
{workflows{
    templates{
      edges{
        node{
          name
          id
          workflowSteps {
            edges {
              node {
                id
                name
                required
                trigger {
                  id
                  required
                  parameters {
                    parameterType
                    required
                  }
                }
                actions {
                  actionKey
                  action {
                    id
                    required
                    parameters {
                      parameterType
                      required
                      helpVariables
                      getOptionsForFieldValue
                      fieldValues
                    }
                  }
                  
                }
                workflowStepCondition {
                  id
                  required
                  conditionalInputParameters{
                    inputParameter {
                      parameterType
                      required
                    }
                    supportedOperators {
                      symbol
                      description
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
````
NOTE : Fields can be added and removed as per Schema to get detailed result.

Write One : NOT SUPPORTED FOR TEMPLATE PROVIDER


Invoking a QraphQL provider via postman

POST **https://workflowautomation-<env>.api.intuit.com/v4/graphql?**   Where values for <env> : qal, e2e

Required Headers:

1. Authorization:

Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<Token Value>",intuit_apikey="<API_Key>",intuit_appid="<PLUGIN APP_ID>",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'


2. intuit_tid:  

NOTE : Pass the query in GraphQl query in GraphQl section of Postman.  Sample Curl for QAL Environment :

`POST /v4/graphql HTTP/1.1
Host: workflowautomation-qal.api.intuit.com
Authorization: Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<Token Value>",intuit_apikey="<API_Key>",intuit_appid="<PLUGIN APP_ID>",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'
intuit_tid: <TID>
Content-Type: application/json
User-Agent: PostmanRuntime/7.21.0
Accept: */*
Cache-Control: no-cache
Postman-Token: 0a390d92-0f77-4afa-97a2-5c4d20203683,f9f21cff-635f-4a73-ba11-f97a9b40b34d
Host: workflowautomation-qal.api.intuit.com
Accept-Encoding: gzip, deflate
Content-Length: 1292
Connection: keep-alive
cache-control: no-cache
`

