### Configuration Properties for dev
###
### Spring Cloud Config value for profile=dev
###
### All properties in this profile can be used
### by setting up SPRING_PROFILES_ACTIVE=default.
### If you are using the API, use the name in profile.
### Add your business logic properties for this
### environment here.
###
### https://github.intuit.com/services-config/config-reference-app/wiki/Config-Properties-Setup

app:
  env: chaos-monkey

#to solve conflict issue of <PERSON>ae<PERSON> and Chaos for metrics bean
spring:
  main:
    allow-bean-definition-overriding: true
    
#Configure chaos monkey - enabled = false
chaos:
  monkey:
    enabled: true  # Disabled by default
    watcher:
      restController: false
      controller: true
      service: false
      repository: false
      component: false
    assaults:
      latencyActive: false
      latencyRangeStart: 1000 #Min Latency Range
      latencyRangeEnd: 2000  #Max Latency Range