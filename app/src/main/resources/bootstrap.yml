spring:
  application:
    name: wkflatmnsvc

# IDPS bootstrap properties, alternative is policy-based bootstrap

---
spring:
  config:
    activate:
      on-profile: default
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig,wfaiconfig,ondemandconfig,handlerconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: WkflAtmnSvc-PRE-PRODUCTION-2AU0YR.pd.idps.a.intuit.com
          api_key_id: v2-3fadea6d5cea5
          api_secret_key: ../idps_config/key_v2-3fadea6d5cea5.pem # remove ../ if you get Missing Private Key


---

###################################### qala Profile ######################################

spring:
  datasource:
    password: "{secret}idps:/qal/rds/db-app-password"
  liquibase:
    password: "{secret}idps:/qal/rds/db-password"
  config:
    activate:
      on-profile: qala
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig,wfaiconfig,ondemandconfig,handlerconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: WkflAtmnSvc-PRE-PRODUCTION-2AU0YR.pd.idps.a.intuit.com
          policy-id: p-iqoafa1fhvcq
          access-type: kube

---

###################################### e2ea Profile ######################################

spring:
  datasource:
    password: "{secret}idps:/e2e/rds/db-app-password"
  liquibase:
    password: "{secret}idps:/e2e/rds/db-password"
  config:
    activate:
      on-profile: e2ea
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig,wfaiconfig,ondemandconfig,handlerconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: WkflAtmnSvc-PRE-PRODUCTION-2AU0YR.pd.idps.a.intuit.com
          policy-id: p-0qm62202g9gy
          access-type: kube

---
###################################### prfa Profile ######################################

spring:
  datasource:
    password: "{secret}idps:/prf/rds/db-app-password"
  liquibase:
    password: "{secret}idps:/prf/rds/db-password"
  config:
    activate:
      on-profile: prfa
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig,ondemandconfig,handlerconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: WkflAtmnSvc-PRE-PRODUCTION-2AU0YR.pd.idps.a.intuit.com
          policy-id: p-xnpf3v4q4u9e
          access-type: kube

launchDarklySdk:
  key: "{secret}idps:/prf/launch-darkly-sdk-key"

---
###################################### prda Profile ######################################

spring:
  datasource:
    password: "{secret}idps:/prd/rds/db-password"
  liquibase:
    password: "{secret}idps:/prd/rds/db-password"
  config:
    activate:
      on-profile: prda
  cloud:
    config:
      uri: https://config.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig,wfaiconfig,ondemandconfig,handlerconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: WkflAtmnSvc-PRODUCTION-7L172L.pd.idps.a.intuit.com
          policy-id: p-ygpeuf2o8yc0
          access-type: kube
          cipher-pool-size: 300 #https://stackoverflow.intuit.com/questions/19119

launchDarklySdk:
  key: "{secret}idps:/prd/launch-darkly-sdk-key"

---
###################################### qalb Profile ######################################

spring:
  config:
    activate:
      on-profile: qalb
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig,ondemandconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: vkm-e2e.ps.idps.a.intuit.com
          policy-id: p-rzd7ea3fm040
          access-type: kube

rds:
  appPassword: "{secret}idps:/qalb/rds/db-app-password"
  adminPassword: "{secret}idps:/qalb/rds/db-password"

---

###################################### e2eb Profile ######################################

spring:
  config:
    activate:
      on-profile: e2eb
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig,ondemandconfig


security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: vkm-e2e.ps.idps.a.intuit.com
          policy-id: p-j2wu95kfdz3m
          access-type: kube

rds:
  appPassword: "{secret}idps:/e2eb/rds/db-app-password"
  adminPassword: "{secret}idps:/e2eb/rds/db-password"

---

###################################### prfb Profile ######################################

spring:
  config:
    activate:
      on-profile: prfb
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: WkflAtmnSvc-PRE-PRODUCTION-2AU0YR.pd.idps.a.intuit.com
          policy-id: p-7o5ff9sw24yp
          access-type: kube

rds:
  appPassword: "{secret}idps:/prf/rds/db-app-password"
  adminPassword: "{secret}idps:/prf/rds/db-password"

launchDarklySdk:
  key: "{secret}idps:/prf/launch-darkly-sdk-key"

---
###################################### prdb Profile ######################################

spring:
  config:
    activate:
      on-profile: prdb
  cloud:
    config:
      uri: https://config.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig,ondemandconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: vkm.ps.idps.a.intuit.com
          policy-id: p-v55i0ns642du
          access-type: kube


rds:
  appPassword: "{secret}idps:/prdb/rds/db-app-password"
  adminPassword: "{secret}idps:/prdb/rds/db-password"

launchDarklySdk:
  key: "{secret}idps:/prdb/launch-darkly-sdk-key"

---
###################################### qalb use2 Profile ######################################

spring:
  config:
    activate:
      on-profile: qalb-use2
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: vkm-e2e.ps.idps.a.intuit.com
          policy-id: p-qvfip0h2kfer
          access-type: kube

rds:
  appPassword: "{secret}idps:/qalb/rds/db-app-password"
  adminPassword: "{secret}idps:/qalb/rds/db-password"

---

###################################### e2eb use2 Profile ######################################

spring:
  config:
    activate:
      on-profile: e2eb-use2
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: vkm-e2e.ps.idps.a.intuit.com
          policy-id: p-smvkibq5wybq
          access-type: kube

rds:
  appPassword: "{secret}idps:/e2eb/rds/db-app-password"
  adminPassword: "{secret}idps:/e2eb/rds/db-password"

---
###################################### prdb use2 Profile ######################################

spring:
  config:
    activate:
      on-profile: prdb-use2
  cloud:
    config:
      uri: https://config.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,ondemandconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: vkm.ps.idps.a.intuit.com
          policy-id: p-79jk42rtu0lf
          access-type: kube

rds:
  appPassword: "{secret}idps:/prdb/rds/db-app-password"
  adminPassword: "{secret}idps:/prdb/rds/db-password"

launchDarklySdk:
  key: "{secret}idps:/prdb/launch-darkly-sdk-key"
---

###################################### e2ec Profile ######################################

spring:
  config:
    activate:
      on-profile: e2ec
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig


security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: vkm-e2e.ps.idps.a.intuit.com
          policy-id: p-x74fsor28jfl
          force_generic_policies: true

rds:
  appPassword: "{secret}idps:/e2ec/rds/db-app-password" #TODO Create new path for e2ec
  adminPassword: "{secret}idps:/e2ec/rds/db-password"

---

###################################### e2ec use2 Profile ######################################

spring:
  config:
    activate:
      on-profile: e2ec-use2
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: vkm-e2e.ps.idps.a.intuit.com
          policy-id: p-sau2luz1sdlu
          force_generic_policies: true

rds:
  appPassword: "{secret}idps:/e2ec/rds/db-app-password"
  adminPassword: "{secret}idps:/e2ec/rds/db-password"

---
###################################### prdc Profile ######################################

spring:
  config:
    activate:
      on-profile: prdc
  cloud:
    config:
      uri: https://config.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,zeroStateConfig,ondemandconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: vkm.ps.idps.a.intuit.com
          policy-id: p-nrlnn2i2lj2e
          force_generic_policies: true


rds:
  appPassword: "{secret}idps:/prdc/rds/db-app-password"
  adminPassword: "{secret}idps:/prdc/rds/db-password"

---
###################################### prdc use2 Profile ######################################

spring:
  config:
    activate:
      on-profile: prdc-use2
  cloud:
    config:
      uri: https://config.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig,ondemandconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: vkm.ps.idps.a.intuit.com
          policy-id: p-hh20b8124krp
          force_generic_policies: true

rds:
  appPassword: "{secret}idps:/prdc/rds/db-app-password"
  adminPassword: "{secret}idps:/prdc/rds/db-password"
---