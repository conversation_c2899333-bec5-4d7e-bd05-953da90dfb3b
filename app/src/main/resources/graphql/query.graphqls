type Query {
    """
    Get Workflow details based on workflowId.
    """
    workflow(id: ID!): Workflow

    """
    Get List of Workflow Task details based on workflowId.
    """
    workflowTasks(workflowId: ID!,taskActivityId: ID,taskExternalId:ID,filter: WorkflowTaskFilter): [WorkflowTask]

    """
    Get Workflow Task details based on workflowId,taskActivityId or  workflowId,taskActivityId,externalTaskId.
    """
    workflowTask(workflowId: ID!,taskActivityId: ID!, taskExternalId:ID): WorkflowTask
}