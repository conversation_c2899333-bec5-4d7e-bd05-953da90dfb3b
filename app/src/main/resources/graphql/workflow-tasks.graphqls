type Workflow {
	id: ID!             		    # workflow instance id
	definitionId: String		 	# workflow definition Id
	recordId: ID       			    # can be invoice-id,engagement-id etc
	name: String		 			# workflow name
	version: String					# workflow version
}

"""
WorkflowTask Details
"""
type WorkflowTask {
    taskActivityId: ID!      		      # Id of bpmn activity
    name: String		 				  # task name
    type: WorkflowTaskType       		  # type of task
    activityType: String				  # type of activity
    description: String    				  # task description
    attributes: [WorkflowTaskAttributes]  # attributes of task.
    taskExternalId: ID              	  # external task id of camunda
    taskRefId: ID       				  # txnId of downstream system like task service id
    created_date: String				  # task creation date
    updated_date: String				  # task updated date
    completed_date: String		          # task completed date
    status: String		 		          # open,created,in-process,completed
   
}

"""
Enum for workflow Task Types
"""
enum WorkflowTaskType {
  HUMAN_TASK
  SYSTEM_TASK
  NOTIFICATION_TASK
}


"""
Workflow Task Attributes like assignee, due-date as key value pair along with property bag.
"""
type WorkflowTaskAttributes {
  name : String
  value: String
}

"""
Workflow Task Details. Either taskExternalId or taskRefId needs to be passed.
"""
input WorkflowTaskInput {
  taskExternalId: ID				  # external task id of camunda
  taskRefId: ID      			  	  # txnId of downstream system like task service id
  status : String!					  # open,created,in-process,completed
  name : String
  attributes: [WorkflowTaskMutationAttributes]
}

"""
Workflow Task Details.
"""
input WorkflowInput {
  id: ID!               	    # workflow instance id
  recordId: ID       			# can be invoice-id,engagement-id etc
  task: WorkflowTaskInput!
}


"""
Workflow Task Mutation Attributes.
"""
input WorkflowTaskMutationAttributes {
  name : String
  value: String
}
