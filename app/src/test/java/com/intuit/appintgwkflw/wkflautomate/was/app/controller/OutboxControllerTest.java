package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.service.DomainEventTestService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class OutboxControllerTest {

  @InjectMocks private OutboxController outboxController;
  @Mock private DomainEventTestService domainEventTestService;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testGetEventsByEntityId() {
    UUID eventId = UUID.randomUUID();
    Mockito.when(domainEventTestService.getEventById(eventId.toString()))
        .thenReturn(DomainEvent.builder().eventId(eventId).build());
    outboxController.getEventsByEntityId(eventId.toString());
    Mockito.verify(domainEventTestService).getEventById(Mockito.any());
  }

  @Test
  public void testGetEventsByPartitionKey() {
    String partitionKey = "partitionKey";

    Mockito.when(domainEventTestService.getEventsByPartitionKey(partitionKey))
        .thenReturn(
            List.of(
                DomainEvent.builder()
                    .eventId(UUID.randomUUID())
                    .partitionKey(partitionKey)
                    .build()));
    outboxController.getEventsByPartitionKey(partitionKey);
    Mockito.verify(domainEventTestService).getEventsByPartitionKey(Mockito.any());
  }

  @Test
  public void testGetCountByEventsPublished() {
    String partitionKey = "partitionKey";
    Mockito.when(domainEventTestService.getCountByEventsPublishedByPartitionKey(partitionKey))
        .thenReturn(1l);
    outboxController.getCountByEventsPublished(partitionKey);
    Mockito.verify(domainEventTestService).getCountByEventsPublishedByPartitionKey(Mockito.any());
  }
}
