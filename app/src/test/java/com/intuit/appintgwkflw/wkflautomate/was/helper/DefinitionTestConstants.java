package com.intuit.appintgwkflw.wkflautomate.was.helper;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DefinitionTestConstants {

  public static final String REALM_ID = "1234";
  public static final String TEMPLATE_ID = "1234";
  public static final String WORKFLOW_STEP_ID = "step-id";
  public static final String TRIGGER_ID = "waitForTimerToElapse1_invoiceApproval_companyId_uuid";
  public static final String ACTION_ID = "sendApprovalEmail_invoiceApproval_companyId_uuid";
  public static final String CONDITION_ID_XOR =
      "evaluateUserDefinedAction_invoiceApproval_companyId_uuid";
  public static final String CONDITION_ID_DMN =
      "invoiceApprovalDecision_invoiceApproval_companyId_uuid";
  public static final String MAPPED_KEY_DMN = "approvalRequired";
  public static final String MAPPED_KEY_XOR = "sendReminderEmail_invoiceApproval_companyId_uuid";
  public static final String DEFINITION_NAME = "defintion-1";
  public static final String DEF_ID = "def_id";
  public static final String ACTION_STEP_ID = "action-id";
  public static final String CONDITION_STEP_ID = "condition-id";
  public static final String YES_PATH_STEP_ID = "yes-id";
}
