### Configuration Properties for dev
###
### Spring Cloud Config value for profile=dev
###
### All properties in this profile can be used
### by setting up SPRING_PROFILES_ACTIVE=default.
### If you are using the API, use the name in profile.
### Add your business logic properties for this 
### environment here.
###
### https://github.intuit.com/services-config/config-reference-app/wiki/Config-Properties-Setup

app:
  env: default
  description: This app properties was built by config-onboarding.


spring:
  datasource:
    password: Intuit01
    url: ****************************************
    username: sas
  jpa:
    open-in-view: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect

access:
  host: access-e2e.platform.intuit.com

# WorkflowCore host endpoint
workflowcore:
  engine:
    host: "http://localhost:8888"
    endpoint: ${workflowcore.engine.host}/rest
    definition:
      deployment: "/deployment/create"
      deployment_crud: "/deployment"
    process:
      definition: "/process-definition"
      instance: "/process-instance"
    start:
      instance: "/start"
    decision:
      definition: "/decision-definition"
    evaluate:
      decision:
        definition: "/evaluate"
    suspend:
      instance: "/suspended"
    externalTask:
      endpoint: "/external-task"
      complete: "/complete"
      failure: "/failure"
    history:
      endpoint: /history
      process-instance: /process-instance/


system-user:
  userName: <EMAIL>
  password: "{secret}idps:/ppd/systemUser/password"

idps:
  offline:
    keyName: "default/offline-ticket-key"
    
appconnect:
  host: https://localhost:8888
  providerAppId: AP13072

retry:
  statusCode : 
    - 500
    - 502
    - 504

resilience4j.circuitbreaker:  
  instances:  
    camunda: 
      #The time that the CircuitBreaker should wait before transitioning from open to half-open 
      waitDurationInOpenState: 10000 
      #Size of the sliding window which is used to record the outcome of calls when the CircuitBreaker is closed.
      slidingWindowSize: 100
      #failure rate threshold in percentage 
      failureRateThreshold: 50  
      #duration threshold above which calls are considered as slow  
      slowCallDurationThreshold: 25000  
      #percentage of slow calls is equal or greater the threshold, the CircuitBreaker transitions to open 
      slowCallRateThreshold: 80  
      recordExceptions: 
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException

        
    appConnect: 
      registerHealthIndicator: true 
      ringBufferSizeInClosedState: 5  
      ringBufferSizeInHalfOpenState: 3  
      #The time that the CircuitBreaker should wait before transitioning from open to half-open 
      waitDurationInOpenState: 10s  
      #failure rate threshold in percentage 
      failureRateThreshold: 50  
      #duration threshold above which calls are considered as slow  
      slowCallDurationThreshold: 30000  
      #percentage of slow calls is equal or greater the threshold, the CircuitBreaker transitions to open 
      slowCallRateThreshold: 100  
      recordExceptions: 
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException
           

resilience4j.retry: 
  instances:  
    wasDB:  
      maxRetryAttempts: 3 
      waitDuration: 100 
      retryExceptions:
        - java.io.IOException 
        - java.util.concurrent.TimeoutException
    wasHttpClient: 
      maxRetryAttempts: 5
      waitDuration: 1000
      retryExceptions:
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException
    camunda: 
      maxRetryAttempts: 5
      waitDuration: 1000
      retryExceptions:
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException
    appConnect: 
      maxRetryAttempts: 5
      waitDuration: 1000
      retryExceptions:
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException

batch-job:
  enabled: true
  cron: "0 0 18 * * *"
  jobName: "workflowBatchJob"
  threadPoolSize: 5
  chunkSize: 1 # Number of Items committed to DB as a batch . Value should be less than BatchSize. Since write takes so
  keepAliveTime: 300 # 5 mins. Since Some job step takes more than 1 minute (with retries)
  batchSize: 5 # Page Size for a Single Read. Starting with lesser value, since it also reads BPMN Data.
  stepConfig:
    cleanupDefinition:
      enabled: true
      isCascade: false
      templateName: customReminder
      cleanupStatus:
        - MARKED_FOR_DELETE
        - MARKED_FOR_DISABLE
      cleanErrorProcess: true
      baseTimeJobStart: 15
      durationInMinutes: 15
      priority: 1
    userToSingleDefinitionMigration:
      enabled: true
      templateName: customReminder
      recordTypes:
        - invoice
        - bill
      baseTimeJobStart: 15
      durationInMinutes: 15
      priority: 2
    singleToSingleDefinitionMigration:
      enabled: true
      templateName: customReminder # Template name for which definition migration will happen
      templateVersion: "3" # List of Template versions for which definition migration will happen, keep an empty string for all versions
      recordTypes: "'invoice','bill'" #Comma separated list of recordTypes without space for which migration will be enabled
      durationInMinutes: 15
      priority: 3

authz:
  env: QAL
  connectionTimeOutMs: 1000