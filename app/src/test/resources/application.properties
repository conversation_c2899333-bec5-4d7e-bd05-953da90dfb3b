security.intuit.ignored=/ping, /health/**, /swagger.json, /swagger.yaml, /*/** 
security.intuit.mock-ticket-client=true
jsk.lastmile.enabled=false
security.require-ssl=false
idps.offline.keyName=test/offline-ticket-key
security.intuit.appId=Intuit.appintgwkflw.wkflautomate.wfas
security.intuit.appSecret={secret}idps:/common/app-secret
app.app-id=${security.intuit.appId}
app.app-secret=${security.intuit.appSecret}
access.host=access-e2e.platform.intuit.com
offline-ticket.settings.was.app-id=${security.intuit.appId}
offline-ticket.settings.was.app-secret=${security.intuit.appSecret}
offline-ticket.settings.was.asset-id=7065039507767760447
rest.max-connections-per-host=30
rest.max-total-connections=50
spring.jpa.properties.hibernate.hbm2ddl.auto=none
spring.batch.job.enabled=false
offline-ticket.refresh-job.cron=0 0 0 * * *
offline-ticket.renew-days-limit=2
batch-cleanup.cron=0 0 0 * * *
server.port=8888
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
graphql-config.services.IDENTITY.url=https://identity-e2e.api.intuit.com/v2/graphql
okhttp-client.maxRequests=700
okhttp-client.maxRequestPerHost=800
authn.systemOfflineJobId=9130350531611166
authn.appId=${security.intuit.appId}
authn.appSecret=${security.intuit.appSecret}
authn.environment=E2E
spring.reader.url=xyz
spring.reader.enabled=false
spring.main.allow-bean-definition-overriding=true