db:
  urlPrefix: ********************************
  username: sas
  password: Intuit01

# Maximum value of history ttl in days
history:
  ttl: 30

# External task properties
external-task:
  client:
    endpoint: ${workflowcore.engine.endpoint}
    backOffDisable: false
    backOffInitTime: 1000
    backOffFactor: 2
    backOffMaxTime: 60000
    errorBackOffInitTime: 4000
    errorBackOffFactor: 2
    errorBackOffMaxTime: 120000
    # retry counter if external task fails in marking it complete
    retryCount: 3
    # retry timer for next retry
    retryTimer: 15000
    backoffStrategyName: 'ERROR_EXPONENTIAL'
    slidingWindow: 10

  thread-pool:
    maxQueueSize: 10
    sharedMinThreads: 1
    sharedMaxThreads: 9
    individualMinThreads: 1
    individualMaxThreads: 3
    idleThreadTimeoutSec: 30
    allowedGracefulShutDownTimeSec: 30

  workers:
    global:
      disable: false
      topicName: workflow
      lockDuration: 60000
      maxTasks: 25
      asyncResponseTimeout: 10000

    premium-apps:
      disable: false
      topicName: premium-apps
      lockDuration: 60000
      maxTasks: 10
      asyncResponseTimeout: 10000

    invoice-approval:
      disable: true
      topicName: invoice-approval
      lockDuration: 60000
      maxTasks: 10
      asyncResponseTimeout: 10000

    engagement__123456789:
      disable: false
      topicName: engagement
      lockDuration: 60000
      maxTasks: 10
      asyncResponseTimeout: 10000

    engagement__987654321:
      disable: true
      topicName: engagement
      lockDuration: 60000
      maxTasks: 10
      asyncResponseTimeout: 10000

spring:
  jpa:
    open-in-view: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  datasource:
    password: ${db.password}
    url: ${db.urlPrefix}/camunda
    username: ${db.username}
    hikari:
      schema: was
  liquibase:
    enabled: true
    liquibase-schema: public
  batch:
    table-prefix: public.batch_

template:
  bpmn:
    max-size: 500000
  dmn:
    max-size: 500000

# WorkflowCore Rest Apis
workflowcore:
  engine:
    host: "http://localhost:8080"
    endpoint: ${workflowcore.engine.host}/rest
    definition:
      deployment: "/deployment/create"
      deployment_crud: "/deployment"
    process:
      definition: "/process-definition"
      key: "/key"
      instance: "/process-instance"
      variables: "/variables"
      delete: "/delete"
    start:
      instance: "/start"
    decision:
      definition: "/decision-definition"
    evaluate:
      decision:
        definition: "/evaluate"
    history:
      endpoint: /history
      process-instance: /process-instance/
      process-variable-instance: /variable-instance
      external-task-log: /external-task-log
      external-task-log-count: /external-task-log/count
    variables: "/variables"
    message: "/message"
    suspend:
      instance: "/suspended"
    externalTask:
      endpoint: "/external-task"
      v1Endpoint: "/v1/external-task"
      complete: "/complete"
      failure: "/failure"
      extend-lock: "/extendLock"
    serviceTask:
      endpoint: "/v1/service-task/complete"
    execution:
      endpoint: /execution
      localVariables: /localVariables
    message-async: "/process-instance/message-async"


http-client-config:
  # since we have two routes one to app connect and other to workflow core engine so sharing total connections between two hosts
  maxPerRoute: 50
  maxTotal: 100
  connectionRequestTimeoutMillis: 30000
  connectTimeoutMillis: 30000
  socketTimeoutMillis: 30000
  # 5 minutes
  timeToLiveMillis: 300000
  # 2 minutes
  validateConnectionInactivityMillis: 120000
  # 30 seconds
  idleConnectionTimeoutSecondsMillis: 30000
  # if true will log request and response details
  debug: true

#Kafka Config
event:
  security.protocol: PLAINTEXT
  primary:
    bootStrapServers: localhost:9092
    security.protocol: PLAINTEXT
  idpsconfig:
    endpoint: kafkainfra-pre-production-l10maf.pd.idps.a.intuit.com
    api_secret_key: ../idps_config/key_v2-e3b373bd2284e.pem # remove ../ if you get Missing Private Key
    api_key_id: v2-e3b373bd2284e
    name: KeyedIDPS
  consumer:
    enabled: false
    config:
      security.protocol: PLAINTEXT
      auto.offset.reset: earliest
      max.partition.fetch.bytes: 2000000
      concurrency: 5
      fetch.min.bytes: 100
      receive.buffer.bytes: 67108864
      reconnect.backoff.ms: 5000
      session.timeout.ms: 20000
      retry.backoff.ms: 5000
      fetch.max.wait.ms: 1000
    groupId: workflowAutomationService-local
    enable-secondary: false
    enablePublishController: true
    # number of threads in the kafka listener
    # Number of hosts = 3, Number of topic partitions = 12. Concurrency set as 4 (Number of topic partitions / Number of hosts)
    concurrency: 5
    # refereed to when there is no initial offset in Kafka or if the current offset does not exist any more on the server
    autoOffsetReset: earliest
    # The maximum amount of data per-partition the server will return
    maxPartitionFetchBytes: 2000000
    # The timeout used to detect consumer failures when using Kafka's group management facility. The consumer sends periodic heartbeats to indicate its liveness to the broker. If no heartbeats are received by the broker before the expiration of this session timeout, then the broker will remove this consumer from the group and initiate a rebalance
    sessionTimeoutMs: 20000
    # The minimum amount of data the server should return for a fetch request. If insufficient data is available the request will wait for that much data to accumulate before answering the request
    fetchMinBytes: 100
    # The maximum amount of time the server will block before answering the fetch request if there isn't sufficient data to immediately satisfy the requirement given by fetch.min.bytes
    fetchMaxWaitMs: 1000
    # The size of the TCP receive buffer (SO_RCVBUF) to use when reading data
    receiveBufferBytes: 67108864
    # The base amount of time to wait before attempting to reconnect to a given host.
    reconnectBackoffMs: 5000
    # The amount of time to wait before attempting to retry a failed request to a given topic partition.
    retryBackoffMs: 5000
    retryConfig:
      retryMaxAttempts: 1 # max retry attempts on a topic including the main consumption call
      retryBackoffPeriod: 500 #fixed backoff period for retry
      dlqEnabled: false #control enabling/disabling of DLQ consumer
      dlqConcurrency: 4
      # retry config on DLQ error handler
      # DLQ retries will occur at cumulative intervals of 2min - 5min - 9.5min
      dlqBackoffInitialInterval: 120000 #exponential backoff initial interval: 2min
      dlqBackoffMultiplier: 1.5 #exponential backoff multiplier
      dlqRetryMaxAttempts: 4 # max retry attempts on dlq topic including the main consumption call

      #exponential backoff max interval. the default value is 30sec which is lower than dlqBackoffInitialInterval
      # any interval > maxInterval value is reset to the maxInterval
      # so the max interval value is increased to enable exponentially backed off retries
      dlqBackoffMaxInterval: 2100000
      # max poll interval set to 35min for DLQ to enable retries without consumer rebalance
      dlqMaxPollIntervalMs: 2100000
    enabledEntities:
      externalTask: true
      externalTaskTest: true
      definitionEvent: true
      trigger: true
      workflowTransitionEvents: true
    entityTopicsMapping:
      externalTask:
        - workflow-externaltask-complete-vep
        - workflow-externaltask-complete-vep-dlq
        - workflow-externaltask-complete-test # to be used for testing
      trigger:
        - workflow-trigger-vep
        - workflow-trigger-vep-dlq
        - workflow-trigger-test
      incident:
        - camunda-incident-vep
      serviceTask:
        - camunda-service-task-assigned
      definitionEvent:
        - definition-event-test
      externalTaskTest:
        - workflow-externaltask-complete-vep
      workflowTransitionEvents:
        - camunda-transition-events-qalb

  producer:
    enabled: false
    environment: qa
    enable-secondary: false
    #   Refer to the doc for more details https://docs.google.com/document/d/1auUptwWPtyRMRNZOrZq_4qsu2uJVXhp_ua50LFx6NUM/edit?ts=5f8eaa0d#heading=h.8j0u0inmbjrq
    config:
      security.protocol: PLAINTEXT
      buffer.memory: 20485760
      metadata.max.age.ms: 10000
      request.timeout.ms: 3000
      batch.size: 150000
      reconnect.backoff.ms: 500
      acks: 1
      retries: 10
      retry.backoff.ms: 500
      linger.ms: 100
    #   Delete this config, after the above config changes go through..
    # This property is used when brokers are down. Amount of data will be cached, until, a broker is up(reconnectBackoffMs). Then the data gets processed according to batchSizeBytes
    # At around 100 TPS load(Each pod gets 10-20 TPS), and each payload is 5 KB (including headers & Process Vars). If Brokers are down for 30 seconds(Leader elections in worst case takes this much time)
    # Had noticed issues where, restack of kafka clusters, it takes around 30 seconds, else it takes within a second.
    # requestTimeoutMs is 3 seconds.
    # total size needs to be cached is, 5KB * 20 * 30 * 3 ~ 10 MB ( With buffer making it to 20 MB)
    bufferMemory: 20485760
    # The number of acknowledgments the producer requires the leader to have received before considering a request complete.
    # Default value is 1 (Leader sends the acknowledgement). Needs to be configured, if we see any message loss in future.
    acks: 1
    # number of retries if request fails to send. Since leader elections in worst case takes upto 30 seconds(Cluster restack), so number of retries is 6 seconds tried every
    # retryBackoffMs (5000ms configured below)
    retries: 6
    # The period of time in milliseconds after which we force a refresh of metadata even if we haven't seen any partition leadership changes
    # to proactively discover any new brokers or partitions.
    metadataMaxAgeMs: 10000
    # The configuration controls the maximum amount of time the client will wait for the response of a request.
    # If the response is not received before the timeout elapses the client will resend the request if necessary or fail the request if retries are exhausted
    requestTimeoutMs: 3000
    # The base amount of time to wait before attempting to reconnect to a given host.
    reconnectBackoffMs: 5000
    # The amount of time to wait before attempting to retry a failed request to a given topic partition.
    retryBackoffMs: 5000
    # batch size per partition . Each request is around 3-4 KB (5 KB approx with headers) (With process variables).
    # In 20 TPS/POD, atleast 20*5 KB = 100 KB. (Some buffer 150 KB)
    batchSizeBytes: 150000
    # once we get batchSize worth of records for a partition it will be sent immediately regardless of this setting,
    # however if we have fewer than this many bytes accumulated for this partition we will 'linger' for the specified time
    # waiting for more records to show up.
    lingerMs: 1000
    entityTopicsMapping:
      externalTask: workflow-externaltask-assigned-vep
      externalTaskTest: workflow-externaltask-assigned-test # to be used for testing on local
      incident: workflow-incident-vep
      serviceTask: camunda-service-task-assigned
      workflowDefinition: definition-event-test
      workflowTransitionEvents: workflow-transition-events-qalb
      trigger: workflow-trigger-test

appconnect:
  host: "http://localhost:8444"
  providerAppId: AP13702
  subscriptionEndpoint: ${appconnect.host}/appconnect/api/v1/subscriptions
  endpoints:
    connector: ${appconnect.host}/appconnect/connector
  workflow:
    taskHandler: ${appconnect.host}/appconnect/connector/workflow-task-handler
    crud: ${appconnect.subscriptionEndpoint}/{0}/workflows
    pollingFrequency: 86400

qbo:
  wasApiClient:
    appId: "Intuit.appintgwkflw.wkflautomate.qbowasapiclient"

ius:
  hostUrl: https://accounts-e2e.platform.intuit.com
  endpoints:
    persona: ${ius.hostUrl}/v1/users/%s/personas
    accountantPersona: ${ius.hostUrl}/v1/realms/%s/firms/%s
    realmPersonas: ${ius.hostUrl}/v1/realms/%s/personas
#Task Status event of ServiceTask and External task.
taskStatusEvent:
  allowedUpdateStatusEvent:
    - blocked
    - in-process
    - paused

offerings:
  filter:
    excludeUrlPatterns:
      - /health
      - /ping
      - /swagger
      - /metrics
      - /actuator
  default-offering: qbo
  allowedSystemUserUrls: #Include url patterns if API path is applicable for system-user auth context
    - /v1/history/process/*
    - /v1/processes
    - /v1/sendEvent
    - /v1/template/**
    - /v2/trigger
    - /v1/external-task/*

  #Note: Default dataSource and workflowCore config will be used for any offering which does not explicitly specifiy it
  downstreamServices:
    - offeringId: qbo #This offeringId is used to run liquibase on default DB

workflowtask:
  enable: true
  state-transition-config:
    maxResult: 100
  task-config:
    HUMAN_TASK:
      endpoint: https://accountantworkflow-e2e.api.intuit.com/v4/entities
      nonRetryErrorCodes:
        - INVALID_REQUEST
        - SYSTEM_ERROR
        - AUTHENTICATION_ERROR
        - AUTHORIZATION_ERROR
        - NOT_FOUND_ERROR
        - NOT_ACCEPTABLE_ERROR
        - UNSUPPORTED_MEDIA_TYPE_ERROR
        - VALIDATION_ERROR
        - STALE_STATE_ERROR
        - DUPLICATE_PROJECT_NAME_FOR_CLIENT
    NOTIFICATION_TASK:
      endpoint: https://consumernotification-e2e.api.intuit.com/v1/events
    BATCH_NOTIFICATION_TASK:
      endpoint: https://consumernotification-e2e.api.intuit.com/v1/events/batch

authz:
  env: QAL
  connectionTimeOutMs: 1000

domain-event:
  topic:
    PROCESS:
      topic: qal.foundation.workflow.workflowautomation.process.v1
      enabled: true
    ACTIVITY:
      topic: qal.foundation.workflow.workflowautomation.activity.v1 #to be created later
      enabled: true
    ACTIVITY_RUNTIME:
      topic: qal.foundation.workflow.workflowautomation.activityruntime.v1
      enabled: true
    TEMPLATE:
      topic: qal.foundation.workflow.workflowautomation.template.v1
      enabled: true
    DEFINITION:
      topic: qal.foundation.workflow.workflowautomation.definition.v1
      enabled: true
  region: USW2
  enabled: true

ess-sqs:
  thread-pool:
    minThreads: 1
    maxThreads: 75
    keepAliveTimeInSec: 300
    queueSize: 150
  retry:
    statusCode:
      - 500
      - 502
      - 504

async-processing:
  enabled: false
  workflows:
    - customapproval
    
#ProgressTracker Config.
milestone-progress-config:
   zeroStateConfig:
      - workflow: engagementTTLiveFullService
        milestoneMeta:
           - name: "Client Unassigned"
             beginElement: "Event_1dst8o1"
             endElement: "Event_13x1jdz"
           - name: "Client Assigned"
             beginElement: "Event_0tl0v1i"
             endElement: "Event_0uwmstq"
           - name: "Welcome Call"
             beginElement: "Event_0c5dn9r"
             endElement: "Event_18ozc3h"
           - name: "Gathering Info"
             beginElement: "Event_13zifud"
             endElement: "Event_0xoftqu"
           - name: "Preparing Return"
             beginElement: "Event_1m2u0p3"
             endElement: "Event_1fhx7ai"
           - name: "Evaluation"
             beginElement: "Event_0nucuju"
             endElement: "Event_0z9h1vr"
           - name: "Client Review"
             beginElement: "Event_19jbr6k"
             endElement: "Event_1ueo2o0"
           - name: "Filing"
             beginElement: "Event_0kymp5k"
             endElement: "Event_0udi3fn"
           - name: "Post File"
             beginElement: "Event_0wmsmvo"
             endElement: "Event_19fen38"
        templates:
           - templateId: da154822-4c2f-4478-9c5f-8cafdb77e4d9
             milestoneMeta:
               - name: "Client Unassigned"
                 beginElement: "Event_1dst8o1"
                 endElement: "Event_13x1jdz"
               - name: "Client Assigned"
                 beginElement: "Event_0tl0v1i"
                 endElement: "Event_0uwmstq"
               - name: "Welcome Call"
                 beginElement: "Event_0c5dn9r"
                 endElement: "Event_18ozc3h"
               - name: "Gathering Info"
                 beginElement: "Event_13zifud"
                 endElement: "Event_0xoftqu"
               - name: "Preparing Return"
                 beginElement: "Event_1m2u0p3"
                 endElement: "Event_1fhx7ai"
               - name: "Evaluation"
                 beginElement: "Event_0nucuju"
                 endElement: "Event_0z9h1vr"
               - name: "Client Review"
                 beginElement: "Event_19jbr6k"
                 endElement: "Event_1ueo2o0"
               - name: "Filing"
                 beginElement: "Event_0kymp5k"
                 endElement: "Event_0udi3fn"
               - name: "Post File"
                 beginElement: "Event_0wmsmvo"
                 endElement: "Event_19fen38"


on-demand:
  action-config:
    createTask:
      bill:
        parameters:
          TaskName: "Approval due for Bill [[DocNumber]]"
          TaskType: "QB_BILL"
          ProjectType: "QB_BILL_APPROVAL"
      invoice:
        parameters:
          TaskName: "Approval due for Invoice [[DocNumber]]"
          TaskType: "QB_INVOICE"
          ProjectType: "QB_INVOICE_APPROVAL"
    sendCompanyEmail:
      bill:
        parameters:
          SendTo: "[[CompanyEmail]]"
          IsEmail: "true"
          Message: "Hi,\n\nBill [[DocNumber]] is pending approval. Please approve it at the earliest using this task manager link - https://app.e2e.qbo.intuit.com/app/taskmanager.\n\nNote that bills that are not approved for more than 30 days will be auto rejected.\n\nThanks,\n[[CompanyName]]"
          Subject: "Review Bill [[DocNumber]]"
          consolidateNotifications: "false"
      invoice:
        parameters:
          SendTo: "[[CompanyEmail]]"
          IsEmail: "true"
          Message: "Hi,\n\nInvoice [[DocNumber]] is pending approval. Please approve it at the earliest using this task manager link - https://app.e2e.qbo.intuit.com/app/taskmanager.\n\nNote that invoice that are not approved for more than 30 days will be auto rejected.\n\nThanks,\n[[CompanyName]]"
          Subject: "Review Invoice [[DocNumber]]"
          consolidateNotifications: "false"