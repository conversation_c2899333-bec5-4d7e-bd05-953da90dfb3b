spring:
  application:
    name: wkflatmnsvc

# IDPS bootstrap properties, alternative is policy-based bootstrap

---
spring:
  config:
    activate:
      on-profile: component-test
  cloud:
    config:
      uri: https://config-e2e.api.intuit.com/v2
      label: master
      name: wkflatmnsvc,templateconfig,templates,precannedconfig,connectorconfig,workerconfig

security:
  intuit:
    appSecret: "{secret}idps:/common/app-secret"
    appId: Intuit.appintgwkflw.wkflautomate.wfas

jsk:
  spring:
    config:
      idps:
        connection:
          endpoint: WkflAtmnSvc-PRE-PRODUCTION-2AU0YR.pd.idps.a.intuit.com
          api_key_id: v2-3fadea6d5cea5
          api_secret_key: ../idps_config/key_v2-3fadea6d5cea5.pem # remove ../ if you get Missing Private Key

---
