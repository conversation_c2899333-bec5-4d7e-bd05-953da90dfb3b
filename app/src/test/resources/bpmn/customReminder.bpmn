<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:color="http://www.omg.org/spec/BPMN/non-normative/color/1.0" id="Definitions_1ueeqzk" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0">
    <bpmn:process id="customReminder" name="Reminder Template" isExecutable="true" camunda:historyTimeToLive="7">
        <bpmn:extensionElements>
            <camunda:properties>
                <camunda:property name="workflowName" value="customReminder" />
            </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:startEvent id="customStartEvent" name="start process" camunda:asyncBefore="true">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="stepDetails" value="{   &#34;customStartEvent&#34;: [     &#34;customStartEvent&#34;,     &#34;decisionElement&#34;,     &#34;inclusiveGateway&#34;,     &#34;createTask&#34;,     &#34;sendExternalEmail&#34;,     &#34;sendCompanyEmail&#34;,     &#34;sendPushNotification&#34;   ],   &#34;customWorkflowWaitEvent&#34;: [     &#34;customWorkflowWaitEvent&#34;   ] }" />
                    <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
                    <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
                    <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;: &#34;entityChangeType&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;Id&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;intuit_userid&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;intuit_realmid&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;SyncToken&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;entityType&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;TxnStatus&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;TxnPaymentStatus&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;TxnSendStatus&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;TxnDueDays&#34;, &#34;variableType&#34;: &#34;integer&#34;}]" />
                    <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;appconnect&#34;, &#34;handlerId&#34;: &#34;intuit-workflows/custom-reminder-start-process&#34;, &#34;actionName&#34;: &#34;executeWorkflowAction&#34;}" />
                    <camunda:property name="parameterDetails" value="{}" />
                    <camunda:property name="startableEvents" value="[&#34;newCustomStart&#34;]" />
                    <camunda:property name="targetApi" value="evaluate-and-trigger" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>Flow_0uuma6l</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_0uuma6l" sourceRef="customStartEvent" targetRef="decisionElement" />
        <bpmn:businessRuleTask id="decisionElement" name="DMN Rule Processor" camunda:type="external" camunda:topic="custom-reminders">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"was_dmn_evaluator","actionName": "evaluateDMN"}</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0uuma6l</bpmn:incoming>
            <bpmn:outgoing>Flow_0e5omq2</bpmn:outgoing>
            <bpmn:outgoing>Sequence_decision_result</bpmn:outgoing>
        </bpmn:businessRuleTask>
        <bpmn:sendTask id="sendExternalEmail" name="Send external email" camunda:type="external" camunda:topic="custom-reminders">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                </camunda:inputOutput>
                <camunda:properties>
                    <camunda:property name="events" value="[&#34;end&#34;]" />
                    <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_sendExternalEmail_name&#34;, &#34;description&#34;:&#34;customReminder_sendExternalEmail_description&#34; }" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_sendExternalEmail</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1xjrp7d</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:serviceTask id="createTask" name="Create task" camunda:type="external" camunda:topic="custom-reminders">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                </camunda:inputOutput>
                <camunda:properties>
                    <camunda:property name="events" value="[&#34;end&#34;]" />
                    <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_createTask_name&#34;, &#34;description&#34;:&#34;customReminder_createTask_description&#34; }" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_createTask</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_137bn44</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:receiveTask id="customWorkflowWaitEvent" name="Wait for state change" messageRef="Message_064b9px">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{"taskHandler": "appconnect", "handlerId": "intuit-workflows/custom-reminder-wait", "actionName": "executeWorkflowAction" }</camunda:inputParameter>
                    <camunda:inputParameter name="currentStepDetails">{ "required": false }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="targetApi">trigger</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1necc4x</bpmn:incoming>
            <bpmn:outgoing>Flow_1xevg2t</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:endEvent id="Event_1fgu7ic" name="End the process if create task is false">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1jh72q4</bpmn:incoming>
            <bpmn:messageEventDefinition id="MessageEventDefinition_1qq1zcv" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="custom-reminders" />
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="Flow_0e5omq2" name="Conditions unmatched" sourceRef="decisionElement" targetRef="Event_1dx97ea">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == false}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:endEvent id="Event_1dx97ea" name="DMN condition not satisfied: End process">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0e5omq2</bpmn:incoming>
            <bpmn:messageEventDefinition id="MessageEventDefinition_0swqckt" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="custom-reminders" />
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="Flow_1xevg2t" sourceRef="customWorkflowWaitEvent" targetRef="Event_1hhbrmg" />
        <bpmn:endEvent id="Event_1hhbrmg" name="State change occured">
            <bpmn:incoming>Flow_1xevg2t</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_0ay2jrq" escalationRef="Escalation_0tyjh9j" />
        </bpmn:endEvent>
        <bpmn:subProcess id="Activity_108jjaa" triggeredByEvent="true">
            <bpmn:serviceTask id="closeTask" name="Close Project service task" camunda:type="external" camunda:topic="custom-reminders">
                <bpmn:extensionElements>
                    <camunda:inputOutput>
                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "appconnect",
                            "handlerId": "intuit-workflows/taskmanager-update-task",
                            "actionName": "executeWorkflowAction"
                            }</camunda:inputParameter>
                        <camunda:inputParameter name="parameterDetails">{
                            "projectId": {
                            "fieldValue": [],
                            "handlerFieldName": "Project",
                            "requiredByHandler": true,
                            "requiredByUI": false,
                            "fieldType": "string",
                            "valueType": "PROCESS_VARIABLE"
                            },
                            "Status": {
                            "fieldValue": [
                            "Complete"
                            ],
                            "requiredByHandler": true,
                            "requiredByUI": false,
                            "multiSelect": false,
                            "fieldType": "string"
                            }
                            }</camunda:inputParameter>
                        <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                    </camunda:inputOutput>
                </bpmn:extensionElements>
                <bpmn:incoming>Flow_16ihqz2</bpmn:incoming>
                <bpmn:outgoing>Flow_0pxn6yi</bpmn:outgoing>
            </bpmn:serviceTask>
            <bpmn:startEvent id="Event_0vepyso" name="Close task">
                <bpmn:outgoing>Flow_16ihqz2</bpmn:outgoing>
                <bpmn:escalationEventDefinition id="EscalationEventDefinition_0ghanme" escalationRef="Escalation_0tyjh9j" />
            </bpmn:startEvent>
            <bpmn:endEvent id="Event_0ey8tt4" name="End process">
                <bpmn:extensionElements>
                    <camunda:inputOutput>
                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "was",
                            "handlerId": "",
                            "actionName": "updateProcessStatus"
                            }</camunda:inputParameter>
                    </camunda:inputOutput>
                </bpmn:extensionElements>
                <bpmn:incoming>Flow_1w9beds</bpmn:incoming>
                <bpmn:messageEventDefinition id="MessageEventDefinition_07qnodz" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="custom-reminders" />
            </bpmn:endEvent>
            <bpmn:sequenceFlow id="Flow_0pxn6yi" sourceRef="closeTask" targetRef="eventTaskClosed" />
            <bpmn:sequenceFlow id="Flow_16ihqz2" sourceRef="Event_0vepyso" targetRef="closeTask" />
            <bpmn:intermediateThrowEvent id="eventTaskClosed" name="Audit: Task Closed">
                <bpmn:extensionElements>
                    <camunda:properties>
                        <camunda:property name="events" value="[&#34;start&#34;]" />
                        <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_eventTaskClosed_name&#34;, &#34;description&#34;:&#34;customReminder_eventTaskClosed_description&#34; }" />
                    </camunda:properties>
                </bpmn:extensionElements>
                <bpmn:incoming>Flow_0pxn6yi</bpmn:incoming>
                <bpmn:outgoing>Flow_1w9beds</bpmn:outgoing>
            </bpmn:intermediateThrowEvent>
            <bpmn:sequenceFlow id="Flow_1w9beds" sourceRef="eventTaskClosed" targetRef="Event_0ey8tt4" />
        </bpmn:subProcess>
        <bpmn:boundaryEvent id="Event_1tx361d" name="Expiry Event" attachedToRef="customWorkflowWaitEvent">
            <bpmn:outgoing>Flow_1fep73j</bpmn:outgoing>
            <bpmn:timerEventDefinition id="TimerEventDefinition_1dgqopk">
                <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">P15D</bpmn:timeDuration>
            </bpmn:timerEventDefinition>
        </bpmn:boundaryEvent>
        <bpmn:sequenceFlow id="Flow_1fep73j" sourceRef="Event_1tx361d" targetRef="eventTaskExpired" />
        <bpmn:endEvent id="Event_0kb35fk" name="End process after the process expiration.">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_18nekhy</bpmn:incoming>
            <bpmn:messageEventDefinition id="MessageEventDefinition_0vwg5fd" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="custom-reminders" />
        </bpmn:endEvent>
        <bpmn:inclusiveGateway id="Gateway_1rgsx47" name="create task?" default="Flow_1jh72q4">
            <bpmn:incoming>SequenceFlow_137bn44</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_0st8w12</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_02eopl1</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_1xjrp7d</bpmn:incoming>
            <bpmn:incoming>Flow_0u990m4</bpmn:incoming>
            <bpmn:outgoing>Flow_1necc4x</bpmn:outgoing>
            <bpmn:outgoing>Flow_1jh72q4</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sendTask id="sendCompanyEmail" name="Send company email" camunda:type="external" camunda:topic="custom-reminders">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                </camunda:inputOutput>
                <camunda:properties>
                    <camunda:property name="events" value="[&#34;end&#34;]" />
                    <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_sendCompanyEmail_name&#34;, &#34;description&#34;:&#34;customReminder_sendCompanyEmail_description&#34; }" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_sendCompanyEmail</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0st8w12</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:sendTask id="sendPushNotification" name="Send push notification" camunda:type="external" camunda:topic="custom-reminders">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                </camunda:inputOutput>
                <camunda:properties>
                    <camunda:property name="events" value="[&#34;end&#34;]" />
                    <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_sendPushNotification_name&#34;, &#34;description&#34;:&#34;customReminder_sendPushNotification_description&#34; }" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_sendPushNotification</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_02eopl1</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:sequenceFlow id="Sequence_decision_result" name="Rule Evaluation Passed" sourceRef="decisionElement" targetRef="inclusiveGateway">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_1necc4x" sourceRef="Gateway_1rgsx47" targetRef="customWorkflowWaitEvent">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('projectId') != null &amp;&amp; execution.getVariable('closeTaskRule') != "close_manually"}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:inclusiveGateway id="inclusiveGateway" name="Gateway">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Sequence_decision_result</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_createTask</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_sendCompanyEmail</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_sendExternalEmail</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_sendPushNotification</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_sendTrayNotification</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="SequenceFlow_createTask" name="Create Task" sourceRef="inclusiveGateway" targetRef="createTask">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("createTask") == true) &amp;&amp; (execution.getVariable("createTask") == true)}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_sendCompanyEmail" name="Send Company Email" sourceRef="inclusiveGateway" targetRef="sendCompanyEmail">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("sendCompanyEmail") == true) &amp;&amp; (execution.getVariable("sendCompanyEmail") == true)}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_137bn44" sourceRef="createTask" targetRef="Gateway_1rgsx47" />
        <bpmn:sequenceFlow id="SequenceFlow_1xjrp7d" sourceRef="sendExternalEmail" targetRef="Gateway_1rgsx47" />
        <bpmn:sequenceFlow id="SequenceFlow_0st8w12" sourceRef="sendCompanyEmail" targetRef="Gateway_1rgsx47" />
        <bpmn:sequenceFlow id="SequenceFlow_02eopl1" sourceRef="sendPushNotification" targetRef="Gateway_1rgsx47" />
        <bpmn:sequenceFlow id="SequenceFlow_sendExternalEmail" name="Send External Email" sourceRef="inclusiveGateway" targetRef="sendExternalEmail">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("sendExternalEmail") == true) &amp;&amp; (execution.getVariable("sendExternalEmail") == true)}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_sendPushNotification" name="Send Push Notification" sourceRef="inclusiveGateway" targetRef="sendPushNotification">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("sendPushNotification") == true) &amp;&amp; (execution.getVariable("sendPushNotification") == true)}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:subProcess id="SubProcess_0g6pybq" triggeredByEvent="true">
            <bpmn:startEvent id="customDeleteVoidTransactionEvent" name="Downgrade">
                <bpmn:outgoing>SequenceFlow_10dp848</bpmn:outgoing>
                <bpmn:outgoing>SequenceFlow_0y51f29</bpmn:outgoing>
                <bpmn:messageEventDefinition id="MessageEventDefinition_02j8ti4" messageRef="Message_1o1rayd" />
            </bpmn:startEvent>
            <bpmn:endEvent id="EndEvent_1adbxdq" name="End process">
                <bpmn:extensionElements>
                    <camunda:inputOutput>
                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "was",
                            "handlerId": "",
                            "actionName": "updateProcessStatus"
                            }</camunda:inputParameter>
                    </camunda:inputOutput>
                </bpmn:extensionElements>
                <bpmn:incoming>SequenceFlow_10dp848</bpmn:incoming>
                <bpmn:incoming>Flow_0yf95mn</bpmn:incoming>
                <bpmn:messageEventDefinition id="MessageEventDefinition_0dzw8q3" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="custom-reminders" />
            </bpmn:endEvent>
            <bpmn:sequenceFlow id="SequenceFlow_10dp848" sourceRef="customDeleteVoidTransactionEvent" targetRef="EndEvent_1adbxdq" />
            <bpmn:sequenceFlow id="SequenceFlow_0y51f29" sourceRef="customDeleteVoidTransactionEvent" targetRef="eventDowngrade" />
            <bpmn:intermediateThrowEvent id="eventDowngrade" name="Audit: downgrade">
                <bpmn:extensionElements>
                    <camunda:properties>
                        <camunda:property name="events" value="[&#34;start&#34;]" />
                        <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_eventDowngrade_name&#34;, &#34;description&#34;:&#34;customReminder_eventDowngrade_description&#34; }" />
                    </camunda:properties>
                </bpmn:extensionElements>
                <bpmn:incoming>SequenceFlow_0y51f29</bpmn:incoming>
                <bpmn:outgoing>Flow_0yf95mn</bpmn:outgoing>
            </bpmn:intermediateThrowEvent>
            <bpmn:sequenceFlow id="Flow_0yf95mn" sourceRef="eventDowngrade" targetRef="EndEvent_1adbxdq" />
        </bpmn:subProcess>
        <bpmn:subProcess id="SubProcess_0jw9z9g" triggeredByEvent="true">
            <bpmn:startEvent id="StartEvent_06qdmrv" name="Entity Delete">
                <bpmn:extensionElements>
                    <camunda:properties>
                        <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;appconnect&#34;, &#34;handlerId&#34;: &#34;intuit-workflows/entity-deleted-custom-workflow&#34;, &#34;actionName&#34;: &#34;executeWorkflowAction&#34; }" />
                        <camunda:property name="targetApi" value="trigger" />
                    </camunda:properties>
                </bpmn:extensionElements>
                <bpmn:outgoing>SequenceFlow_0yb8p84</bpmn:outgoing>
                <bpmn:messageEventDefinition id="MessageEventDefinition_19pdfcz" messageRef="Message_1qdj486" />
            </bpmn:startEvent>
            <bpmn:sequenceFlow id="SequenceFlow_0yb8p84" sourceRef="StartEvent_06qdmrv" targetRef="eventEntityDeleted" />
            <bpmn:endEvent id="EndEvent_02ic7kr" name="End process">
                <bpmn:extensionElements>
                    <camunda:inputOutput>
                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "was",
                            "handlerId": "",
                            "actionName": "updateProcessStatus"
                            }</camunda:inputParameter>
                    </camunda:inputOutput>
                </bpmn:extensionElements>
                <bpmn:incoming>Flow_1858lvn</bpmn:incoming>
                <bpmn:escalationEventDefinition escalationRef="Escalation_0tyjh9j" />
            </bpmn:endEvent>
            <bpmn:intermediateThrowEvent id="eventEntityDeleted" name="Audit: Entity deleted">
                <bpmn:extensionElements>
                    <camunda:properties>
                        <camunda:property name="events" value="[&#34;start&#34;]" />
                        <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_eventEntityDeleted_name&#34;, &#34;description&#34;:&#34;customReminder_eventEntityDeleted_description&#34; }" />
                    </camunda:properties>
                </bpmn:extensionElements>
                <bpmn:incoming>SequenceFlow_0yb8p84</bpmn:incoming>
                <bpmn:outgoing>Flow_1858lvn</bpmn:outgoing>
            </bpmn:intermediateThrowEvent>
            <bpmn:sequenceFlow id="Flow_1858lvn" sourceRef="eventEntityDeleted" targetRef="EndEvent_02ic7kr" />
        </bpmn:subProcess>
        <bpmn:intermediateThrowEvent id="eventTaskExpired" name="Audit: Task Expired">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="events" value="[&#34;start&#34;]" />
                    <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_eventTaskExpired_name&#34;, &#34;description&#34;:&#34;customReminder_eventTaskExpired_description&#34; }" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1fep73j</bpmn:incoming>
            <bpmn:outgoing>Flow_18nekhy</bpmn:outgoing>
        </bpmn:intermediateThrowEvent>
        <bpmn:sequenceFlow id="Flow_18nekhy" sourceRef="eventTaskExpired" targetRef="Event_0kb35fk" />
        <bpmn:sequenceFlow id="Flow_1jh72q4" sourceRef="Gateway_1rgsx47" targetRef="Event_1fgu7ic" />
        <bpmn:sequenceFlow id="SequenceFlow_sendTrayNotification" name="Send Tray Notification" sourceRef="inclusiveGateway" targetRef="sendTrayNotification">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("sendTrayNotification") == true) &amp;&amp; (execution.getVariable("sendTrayNotification") == true)}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_0u990m4" sourceRef="sendTrayNotification" targetRef="Gateway_1rgsx47" />
        <bpmn:serviceTask id="sendTrayNotification" name="Send tray notification" camunda:type="external" camunda:topic="custom-reminders">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="notificationData">{}</camunda:inputParameter>
                    <camunda:inputParameter name="notificationMetaData">{"authId": ${intuit_userid}, "primaryId": ${intuit_realmid}, "androidDeviceTokens": ["token1", "token2"]}</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
                </camunda:inputOutput>
                <camunda:properties>
                    <camunda:property name="type" value="NOTIFICATION_TASK" />
                    <camunda:property name="taskDetails" value="{&#34;fatal&#34;: false}" />
                    <camunda:property name="serviceName" value="test" />
                    <camunda:property name="notificationName" value="test" />
                    <camunda:property name="notificationDataType" value="test" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_sendTrayNotification</bpmn:incoming>
            <bpmn:outgoing>Flow_0u990m4</bpmn:outgoing>
        </bpmn:serviceTask>
    </bpmn:process>
    <bpmn:escalation id="Escalation_0tyjh9j" name="close_task" escalationCode="closetask" />
    <bpmn:message id="Message_0p07vw4" name="process_ended_message" />
    <bpmn:message id="Message_064b9px" name="customWait" />
    <bpmn:message id="Message_1o1rayd" name="deleted_voided_disable" />
    <bpmn:message id="Message_172hcy9" name="cleanup" />
    <bpmn:message id="Message_1qdj486" name="custom_deleted" />
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customReminder">
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="customStartEvent">
                <dc:Bounds x="292" y="412" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="279" y="455" width="63" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_109vnno_di" bpmnElement="decisionElement">
                <dc:Bounds x="420" y="390" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0a7dlcb_di" bpmnElement="sendExternalEmail">
                <dc:Bounds x="840" y="240" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1sjt7qd_di" bpmnElement="createTask">
                <dc:Bounds x="840" y="120" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1guy81g_di" bpmnElement="customWorkflowWaitEvent">
                <dc:Bounds x="1200" y="380" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0xbnmwr_di" bpmnElement="Event_1fgu7ic">
                <dc:Bounds x="1352" y="282" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1325" y="239" width="89" height="40" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1f1cf2c_di" bpmnElement="Event_1dx97ea">
                <dc:Bounds x="522" y="272" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="497" y="229" width="85" height="40" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_00zaz0i_di" bpmnElement="Event_1hhbrmg">
                <dc:Bounds x="1422" y="402" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1408" y="445" width="65" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1risnpd_di" bpmnElement="Event_0kb35fk">
                <dc:Bounds x="1232" y="622" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1207" y="665" width="87" height="40" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_168ziyq_di" bpmnElement="Gateway_1rgsx47">
                <dc:Bounds x="1075" y="395" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1030" y="445" width="60" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0p4g82p_di" bpmnElement="sendCompanyEmail">
                <dc:Bounds x="840" y="380" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0vgc65h_di" bpmnElement="sendPushNotification">
                <dc:Bounds x="840" y="510" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0xoew6n_di" bpmnElement="inclusiveGateway">
                <dc:Bounds x="670" y="405" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="648" y="383" width="44" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1nd6pgn_di" bpmnElement="sendTrayNotification">
                <dc:Bounds x="840" y="650" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubProcess_0g6pybq_di" bpmnElement="SubProcess_0g6pybq" isExpanded="true">
                <dc:Bounds x="280" y="780" width="350" height="200" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="StartEvent_1cl4dtq_di" bpmnElement="customDeleteVoidTransactionEvent">
                <dc:Bounds x="320" y="862" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="311" y="905" width="57" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="EndEvent_1adbxdq_di" bpmnElement="EndEvent_1adbxdq">
                <dc:Bounds x="532" y="862" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="519" y="905" width="62" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0uck4jr" bpmnElement="eventDowngrade" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
                <dc:Bounds x="422" y="862" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="398" y="905" width="86" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0y51f29_di" bpmnElement="SequenceFlow_0y51f29">
                <di:waypoint x="356" y="880" />
                <di:waypoint x="422" y="880" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0yf95mn_di" bpmnElement="Flow_0yf95mn">
                <di:waypoint x="458" y="880" />
                <di:waypoint x="532" y="880" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Event_0krb04c_di" bpmnElement="eventTaskExpired" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
                <dc:Bounds x="1232" y="532" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1173" y="536" width="54" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubProcess_0jw9z9g_di" bpmnElement="SubProcess_0jw9z9g" isExpanded="true">
                <dc:Bounds x="715" y="790" width="350" height="200" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="StartEvent_06qdmrv_di" bpmnElement="StartEvent_06qdmrv">
                <dc:Bounds x="755" y="872" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="743" y="915" width="63" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="EndEvent_11fhe8y_di" bpmnElement="EndEvent_02ic7kr">
                <dc:Bounds x="967" y="872" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="954" y="915" width="62" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_09j5ed6" bpmnElement="eventEntityDeleted" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
                <dc:Bounds x="852" y="872" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="842" y="915" width="59" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0yb8p84_di" bpmnElement="SequenceFlow_0yb8p84">
                <di:waypoint x="791" y="890" />
                <di:waypoint x="852" y="890" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1858lvn_di" bpmnElement="Flow_1858lvn">
                <di:waypoint x="888" y="890" />
                <di:waypoint x="967" y="890" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Activity_108jjaa_di" bpmnElement="Activity_108jjaa" isExpanded="true">
                <dc:Bounds x="160" y="550" width="440" height="200" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1t3pzu3_di" bpmnElement="closeTask">
                <dc:Bounds x="280" y="610" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0vepyso_di" bpmnElement="Event_0vepyso">
                <dc:Bounds x="200" y="632" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="192" y="675" width="52" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0ey8tt4_di" bpmnElement="Event_0ey8tt4">
                <dc:Bounds x="518" y="632" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="505" y="675" width="62" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_14geeeh" bpmnElement="eventTaskClosed" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
                <dc:Bounds x="438" y="632" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="430" y="595" width="54" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="Flow_16ihqz2_di" bpmnElement="Flow_16ihqz2">
                <di:waypoint x="236" y="650" />
                <di:waypoint x="280" y="650" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0pxn6yi_di" bpmnElement="Flow_0pxn6yi">
                <di:waypoint x="380" y="650" />
                <di:waypoint x="438" y="650" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1w9beds_di" bpmnElement="Flow_1w9beds">
                <di:waypoint x="474" y="650" />
                <di:waypoint x="518" y="650" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Event_1fte3ra_di" bpmnElement="Event_1tx361d">
                <dc:Bounds x="1232" y="442" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1221" y="485" width="62" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="Flow_0uuma6l_di" bpmnElement="Flow_0uuma6l">
                <di:waypoint x="328" y="430" />
                <di:waypoint x="420" y="430" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0e5omq2_di" bpmnElement="Flow_0e5omq2">
                <di:waypoint x="470" y="390" />
                <di:waypoint x="470" y="290" />
                <di:waypoint x="522" y="290" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="402" y="319" width="55" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1xevg2t_di" bpmnElement="Flow_1xevg2t">
                <di:waypoint x="1300" y="420" />
                <di:waypoint x="1422" y="420" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1fep73j_di" bpmnElement="Flow_1fep73j">
                <di:waypoint x="1250" y="478" />
                <di:waypoint x="1250" y="532" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0o8v5j8_di" bpmnElement="Sequence_decision_result">
                <di:waypoint x="520" y="430" />
                <di:waypoint x="670" y="430" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="548" y="448" width="78" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1necc4x_di" bpmnElement="Flow_1necc4x">
                <di:waypoint x="1125" y="420" />
                <di:waypoint x="1200" y="420" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1033" y="402" width="22" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0gdfajo_di" bpmnElement="SequenceFlow_createTask">
                <di:waypoint x="695" y="405" />
                <di:waypoint x="695" y="160" />
                <di:waypoint x="840" y="160" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="860" y="213" width="59" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_13dpi54_di" bpmnElement="SequenceFlow_sendCompanyEmail">
                <di:waypoint x="720" y="430" />
                <di:waypoint x="840" y="430" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="852" y="478" width="76" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_137bn44_di" bpmnElement="SequenceFlow_137bn44">
                <di:waypoint x="940" y="160" />
                <di:waypoint x="1100" y="160" />
                <di:waypoint x="1100" y="395" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1xjrp7d_di" bpmnElement="SequenceFlow_1xjrp7d">
                <di:waypoint x="940" y="280" />
                <di:waypoint x="1100" y="280" />
                <di:waypoint x="1100" y="395" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0st8w12_di" bpmnElement="SequenceFlow_0st8w12">
                <di:waypoint x="940" y="420" />
                <di:waypoint x="1075" y="420" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_02eopl1_di" bpmnElement="SequenceFlow_02eopl1">
                <di:waypoint x="940" y="550" />
                <di:waypoint x="1100" y="550" />
                <di:waypoint x="1100" y="445" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0n3artf_di" bpmnElement="SequenceFlow_sendExternalEmail">
                <di:waypoint x="695" y="405" />
                <di:waypoint x="695" y="280" />
                <di:waypoint x="840" y="280" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="855" y="336" width="70" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1nuvc6b_di" bpmnElement="SequenceFlow_sendPushNotification">
                <di:waypoint x="695" y="455" />
                <di:waypoint x="695" y="560" />
                <di:waypoint x="840" y="560" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="852" y="606" width="55" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_18nekhy_di" bpmnElement="Flow_18nekhy">
                <di:waypoint x="1250" y="568" />
                <di:waypoint x="1250" y="622" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1jh72q4_di" bpmnElement="Flow_1jh72q4">
                <di:waypoint x="1125" y="420" />
                <di:waypoint x="1150" y="420" />
                <di:waypoint x="1150" y="300" />
                <di:waypoint x="1352" y="300" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1bexgom_di" bpmnElement="SequenceFlow_sendTrayNotification">
                <di:waypoint x="695" y="455" />
                <di:waypoint x="695" y="690" />
                <di:waypoint x="840" y="690" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="862" y="736" width="55" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0u990m4_di" bpmnElement="Flow_0u990m4">
                <di:waypoint x="940" y="690" />
                <di:waypoint x="1100" y="690" />
                <di:waypoint x="1100" y="445" />
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>