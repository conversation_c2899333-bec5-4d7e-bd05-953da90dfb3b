{"request": {"method": "POST", "url": "/", "bodyPatterns": [{"equalToJson": "[\n  {\n    \"recurrence\": {\n      \"timeZone\": \"America/Los_Angeles\",\n      \"pattern\": {\n        \"type\": \"DAILY\",\n        \"interval\": 1\n      },\n      \"range\": {\n        \"startDate\": \"${json-unit.ignore}\" ,\n        \"endDate\": [\n          9999,\n          12,\n          31\n        ]\n      }\n    },\n    \"$type\": \"/payments/schedule/EventSchedule\"\n  },\n  {\n    \"recurrence\": {\n      \"timeZone\": \"America/Los_Angeles\",\n      \"pattern\": {\n        \"type\": \"DAILY\",\n        \"interval\": 1\n      },\n      \"range\": {\n        \"startDate\": \"${json-unit.ignore}\" ,\n        \"endDate\": [\n          9999,\n          12,\n          31\n        ]\n      }\n    },\n    \"$type\": \"/payments/schedule/EventSchedule\"\n  }\n]"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json", "intuit_companyid": "9130353747084236", "intuit_scheduletype": "USERS"}, "jsonBody": [{"errors": null, "data": [{"id": "djQuMTo5MTMwMzUzNzQ3MDg0MjM2OjY5OWYyZTM2NWM:qal661382962962966", "status": "ACTIVE"}]}, {"errors": null, "data": [{"id": "djQuMTo5MTMwMzUzNzQ3MDg0MjM2OjY5OWYyZTM2NWM:qal190281630151231", "status": "ACTIVE"}]}]}}