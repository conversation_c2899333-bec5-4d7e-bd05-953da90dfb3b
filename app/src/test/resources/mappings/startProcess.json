{"request": {"method": "POST", "urlPattern": "/rest/process-definition/customReminder:1:[a-f0-9\\-]+/start"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"links": [{"method": "GET", "href": "http://localhost:8080/rest/process-instance/c92ab8db-7259-11ee-a4dc-acde48001122", "rel": "self"}], "id": "c92ab8db-7259-11ee-a4dc-acde48001122", "definitionId": "customReminder:1:a8db27aa-7257-11ee-a4dc-acde48001122", "businessKey": "9130353747084236", "caseInstanceId": null, "ended": false, "suspended": false, "tenantId": null, "variables": {"Customer": {"type": "String", "value": "", "valueInfo": {}}, "TxnPaymentStatus": {"type": "String", "value": "UNPAID", "valueInfo": {}}, "TxnAmount": {"type": "Double", "value": 60, "valueInfo": {}}, "CompanyName": {"type": "String", "value": "SwatiEnterprise", "valueInfo": {}}, "sendExternalEmail": {"type": "String", "value": true, "valueInfo": {}}, "CustomerEmail": {"type": "String", "value": "", "valueInfo": {}}, "DocNumber": {"type": "String", "value": "", "valueInfo": {}}, "definitionKey": {"type": "String", "value": "customReminder_9130353747084236_81d19803-ffc2-40c5-95cc-09ff3d106fd6", "valueInfo": {}}, "CustomerName": {"type": "String", "value": "", "valueInfo": {}}, "intuit_realmid": {"type": "String", "value": "9130353747084236", "valueInfo": {}}, "entityChangeType": {"type": "String", "value": "newcustomstart", "valueInfo": {}}, "entityType": {"type": "String", "value": "Invoice", "valueInfo": {}}, "TxnDate": {"type": "String", "value": "2023-05-29", "valueInfo": {}}, "CompanyEmail": {"type": "String", "value": "<EMAIL>", "valueInfo": {}}, "TxnDueDays": {"type": "Integer", "value": null, "valueInfo": {}}, "intuit_userid": {"type": "String", "value": "9130353521960306", "valueInfo": {}}, "sendCompanyEmail": {"type": "String", "value": false, "valueInfo": {}}, "TxnBalanceAmount": {"type": "Double", "value": 60, "valueInfo": {}}, "SyncToken": {"type": "String", "value": 0, "valueInfo": {}}, "TxnCreateDays": {"type": "Integer", "value": 0, "valueInfo": {}}, "TxnDueDate": {"type": "String", "value": "", "valueInfo": {}}, "templateName": {"type": "String", "value": "invoicecustomReminder", "valueInfo": {}}, "sendPushNotification": {"type": "String", "value": false, "valueInfo": {}}, "Id": {"type": "String", "value": "2", "valueInfo": {}}, "TxnStatus": {"type": "String", "value": " ", "valueInfo": {}}, "createTask": {"type": "String", "value": true, "valueInfo": {}}, "TxnSendStatus": {"type": "String", "value": "UNSENT", "valueInfo": {}}}}}}