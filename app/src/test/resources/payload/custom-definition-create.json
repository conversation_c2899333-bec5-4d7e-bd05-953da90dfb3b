{"input_0": {"clientMutationId": "CustomReminder2308dac1", "workflowsDefinition": {"status": "ENABLED", "displayName": "invoice custom swati", "recordType": "invoice", "workflowSteps": [{"id": "djQuMTo5MTMwMzUzNzQ3MDg0MjM2OmMyNjEwYmRmYWI:customStartEvent", "workflowStepCondition": {"id": "djQuMTo5MTMwMzUzNzQ3MDg0MjM2OjEwMzVlOWJmYjc:decisionElement", "ruleLines": [{"rules": [{"parameterName": "TxnAmount", "conditionalExpression": "GT 0", "parameterType": "DOUBLE"}, {"parameterName": "TxnCreateDays", "conditionalExpression": "AF 0", "parameterType": "DAYS"}], "mappedActionKeys": ["decisionR<PERSON>ult"]}]}, "actions": [{"actionKey": "reminder", "action": {"id": "djQuMTo5MTMwMzUzNzQ3MDg0MjM2OmQ2NTUzY2E4NTI:createTask", "selected": true, "name": "Create a task", "required": false, "parameters": [{"required": true, "parameterType": "STRING", "getOptionsForFieldValue": "GET_ADMINS_ID", "parameterName": "Assignee", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": [], "fieldValues": "9130353747084256", "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 0}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "CloseTask", "configurable": true, "multiSelect": null, "possibleFieldValues": ["txn_paid", "txn_sent", "close_manually"], "helpVariables": [], "fieldValues": "txn_paid", "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 0}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "TaskName", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": ["Company Name", "Company Email", "Customer Name", "Customer <PERSON><PERSON>", "Invoice Number", "Total Amount", "Balance", "Due Date", "Invoice Date"], "fieldValues": ["Review Invoice [[Invoice Number]]"], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 0}]}, "__typename": "Workflows_WorkflowStep_ActionMapper"}, {"actionKey": "reminder", "action": {"id": "djQuMTo5MTMwMzUzNzQ3MDg0MjM2OmQ2NTUzY2E4NTI:sendExternalEmail", "selected": true, "name": "Send a customer email", "required": false, "parameters": [{"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "CC", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": [], "fieldValues": [], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 1}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "BCC", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": [], "fieldValues": [], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 1}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "Message", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": ["Company Name", "Company Email", "Customer Name", "Customer <PERSON><PERSON>", "Invoice Number", "Total Amount", "Balance", "Due Date", "Invoice Date"], "fieldValues": ["Hi [[Customer Name]],\n\nInvoice [[Invoice Number]] needs your attention. Please take a look at the attached invoice and contact us if you have any questions.\n\nThanks,\n[[Company Name]]"], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 1}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "SendTo", "configurable": false, "multiSelect": null, "possibleFieldValues": [], "helpVariables": ["Customer <PERSON><PERSON>"], "fieldValues": ["[[Customer Email]]"], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 1}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "Subject", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": ["Company Name", "Company Email", "Customer Name", "Customer <PERSON><PERSON>", "Invoice Number", "Total Amount", "Balance", "Due Date", "Invoice Date"], "fieldValues": ["Invoice [[Invoice Number]] needs your attention"], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 1}]}, "__typename": "Workflows_WorkflowStep_ActionMapper"}, {"actionKey": "reminder", "action": {"id": "djQuMTo5MTMwMzUzNzQ3MDg0MjM2OmQ2NTUzY2E4NTI:sendCompanyEmail", "selected": false, "name": "Send a company email", "required": false, "parameters": [{"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "CC", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": [], "fieldValues": [], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 2}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "BCC", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": [], "fieldValues": [], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 2}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "Message", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": ["Company Name", "Company Email", "Customer Name", "Customer <PERSON><PERSON>", "Invoice Number", "Total Amount", "Balance", "Due Date", "Invoice Date"], "fieldValues": ["Hi,\n\nInvoice [[Invoice Number]] needs your attention. Please take a look at the invoice and complete any outstanding tasks.\n\nThanks,\n[[Company Name]]"], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 2}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": "GET_USERS", "parameterName": "SendTo", "configurable": true, "multiSelect": true, "possibleFieldValues": [], "helpVariables": ["Company Email"], "fieldValues": [""], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 2}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "Subject", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": ["Company Name", "Company Email", "Customer Name", "Customer <PERSON><PERSON>", "Invoice Number", "Total Amount", "Balance", "Due Date", "Invoice Date"], "fieldValues": ["Review Invoice [[Invoice Number]]"], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 2}, {"required": true, "parameterType": "BOOLEAN", "getOptionsForFieldValue": null, "parameterName": "consolidateNotifications", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": [], "fieldValues": ["true"], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 2}]}, "__typename": "Workflows_WorkflowStep_ActionMapper"}, {"actionKey": "reminder", "action": {"id": "djQuMTo5MTMwMzUzNzQ3MDg0MjM2OmQ2NTUzY2E4NTI:sendPushNotification", "selected": false, "name": "Send a push notification", "required": false, "parameters": [{"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "Message", "configurable": false, "multiSelect": null, "possibleFieldValues": [], "helpVariables": ["Company Name", "Company Email", "Customer Name", "Customer <PERSON><PERSON>", "Invoice Number", "Total Amount", "Balance", "Due Date", "Invoice Date"], "fieldValues": ["Go to QuickBooks to view it."], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 3}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "SendTo", "configurable": true, "multiSelect": null, "possibleFieldValues": [], "helpVariables": [], "fieldValues": [], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 3}, {"required": true, "parameterType": "STRING", "getOptionsForFieldValue": null, "parameterName": "Subject", "configurable": false, "multiSelect": null, "possibleFieldValues": [], "helpVariables": ["Company Name", "Company Email", "Customer Name", "Customer <PERSON><PERSON>", "Invoice Number", "Total Amount", "Balance", "Due Date", "Invoice Date"], "fieldValues": ["An Invoice needs your attention"], "__typename": "Workflows_Definitions_InputParameter", "actionIndex": 3}]}, "__typename": "Workflows_WorkflowStep_ActionMapper"}], "trigger": null}]}}}