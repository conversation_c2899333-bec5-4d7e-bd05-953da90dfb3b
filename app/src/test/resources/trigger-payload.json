{"eventHeaders": {"workflow": "approval", "entityType": "invoice", "entityChangeType": "updated"}, "entity": {"Invoice": {"CurrencyRef": {"name": "United States Dollar", "value": "USD"}, "EmailStatus": "NotSet", "AllowOnlineACHPayment": false, "AllowIPNPayment": false, "MetaData": {"CreateTime": "2020-01-30T21:37:01-08:00", "LastUpdatedTime": "2020-01-30T21:37:01-08:00"}, "DocNumber": "1013", "PrintStatus": "NeedToPrint", "DueDate": "2020-03-01", "LinkedTxn": [], "AllowOnlinePayment": false, "TxnDate": "2020-01-31", "DepartmentRef": {"name": "dep1", "value": "1"}, "Line": [{"LineNum": 1, "DetailType": "SalesItemLineDetail", "Amount": 2, "SalesItemLineDetail": {"TaxCodeRef": {"value": "NON"}, "ItemAccountRef": {"name": "Services", "value": "4"}, "ItemRef": {"name": "Gardening", "value": "3"}}, "Id": "1"}, {"SubTotalLineDetail": {}, "DetailType": "SubTotalLineDetail", "Amount": 2}], "SyncToken": "0", "sparse": false, "domain": "QBO", "CustomField": [], "SalesTermRef": {"value": "3"}, "Id": "9", "TotalAmt": 1000, "Tag": [], "AllowOnlineCreditCardPayment": false, "CustomerRef": {"name": "Customer1", "value": "1"}, "Balance": 2, "ApplyTaxAfterDiscount": false}, "time": "2020-01-30T21:37:28.661-08:00"}}