Create template query
---------------------------------
POST /v1/template/save HTTP/1.1
Host: localhost.intuit.com:8443
Authorization: Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid=<company_id>,intuit_token=<ptc_tkt_id>,intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_userid=<user_id>,intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<app-secret>
intuit_tid: <intuit_tid>
Cache-Control: no-cache
Postman-Token: ccd4cc70-abe9-fe24-5696-f2f7097753b9
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="InvoiceApproval_withHandlerDetails.bpmn"
Content-Type: 


------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="template_metadata"

{"status":"enabled","creatorType":"system","allowMultipleDefs":true}
------WebKitFormBoundary7MA4YWxkTrZu0gW--


Update template query
------------------------------------
POST /v1/template/update HTTP/1.1
Host: localhost.intuit.com:8443
Authorization: Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid=<company_id>,intuit_token=<ptc_tkt_id>,intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_userid=<user_id>,intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<app-secret>
intuit_tid: <intuit_tid>
Cache-Control: no-cache
Postman-Token: 119ea5e3-cde3-c5b1-03ee-9127f4bc0210
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="InvoiceApproval_withHandlerDetails.bpmn"
Content-Type: 


------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="template_metadata"

{"status":"enabled","creatorType":"system","allowMultipleDefs":true}
------WebKitFormBoundary7MA4YWxkTrZu0gW--


Enable/ disable template query
----------------------------------------
PUT /v1/template/InvoiceApproval_v1?status=disabled HTTP/1.1
Host: localhost.intuit.com:8443
Authorization: Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid=<company_id>,intuit_token=<ptc_tkt_id>,intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_userid=<user_id>,intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<app-secret>
intuit_tid: <intuit_tid>
Cache-Control: no-cache
Postman-Token: a3e9db11-85de-eefe-eabd-4444ce1e07db
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW


Read template query
-----------------------------------------
GET /v1/template/InvoiceApprovalTest.bpmn HTTP/1.1
Host: localhost.intuit.com:8443
Authorization: Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid=<company_id>,intuit_token=<ptc_tkt_id>,intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_userid=<user_id>,intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<app-secret>
intuit_tid: <intuit_tid>
Cache-Control: no-cache
Postman-Token: d3557e75-3c9f-8274-1183-ace6f16e8dfe
