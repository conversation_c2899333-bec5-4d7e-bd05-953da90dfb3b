# Workflow Automation Service Component Test

## How do I run this locally?

1. Spin up docker containers using following command required to run component test. e.g. MockDB, <PERSON><PERSON><PERSON>

   ```groovy
   cd Component-test
   ```
   ```groovy
   ./component-test-runner.sh docker
   ```

2. Run the service with this script (in root project directory)
   ```shell
   mvn test -Dgroups=componentTest
   ```