package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.ExternalTaskAttributes;
import java.util.Objects;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * Util to map {@link ExternalTaskAssigned} events.
 */
@UtilityClass
public class ExternalTaskPublishHelper {

  /**
   * Maps ExternalTaskAssigned event
   * @param processDetails process details
   * @param externalTaskAttributes containing info
   * @return ext-task assigned event
   */
  public ExternalTaskAssigned buildEventPayload(ProcessDetails processDetails, ExternalTaskAttributes externalTaskAttributes){
    WorkflowMetaData workflowMetaData = WorkflowMetaData.builder()
        .processInstanceId(processDetails.getProcessId())
        .workflowName(processDetails.getDefinitionDetails().getTemplateDetails().getTemplateName())
        .workflowOwnerId(Long.toString(processDetails.getOwnerId()))
        .build();

    ExternalTaskAssigned.ExternalTaskAssignedBuilder externalTaskAssignedBuilder = ExternalTaskAssigned.builder()
        .workflowMetadata(workflowMetaData)
        .businessEntityId(processDetails.getRecordId())
        .businessEntityType(processDetails.getDefinitionDetails().getRecordType().getRecordType())
        .variables(externalTaskAttributes.getVariableMap())
        .extensions(externalTaskAttributes.getExtensionAttributes());
    if(MapUtils.isNotEmpty(externalTaskAttributes.getVariableMap()) &&
        Objects.nonNull(externalTaskAttributes.getVariableMap()
            .get(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE))) {
      externalTaskAssignedBuilder.txnId
          (externalTaskAttributes.getVariableMap().get(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE).toString());
    }
    return externalTaskAssignedBuilder.build();
  }

  public String transformEntityId(String taskId, String workerId) {
    if (StringUtils.isBlank(workerId) || StringUtils.isBlank(taskId)) {
      return null;
    }
    return taskId.concat(WorkflowConstants.COLON)
        .concat(workerId);
  }
}
