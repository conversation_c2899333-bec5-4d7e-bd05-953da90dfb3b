package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomConfigV2;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.MigratedConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.OldCustomWorkflowConfig;
import java.text.MessageFormat;
import java.util.Map;
import java.util.Set;

public class TemplateBuilderTestHelper {
  public static String DICTIONARY_PATH = "schema/testData/dictionary.yaml";
  public static String ROOT_CONFIG_PATH = "schema/testData";
  public static String YAML_KEY = "templateConfig";
  public static String ENTITY_INVOICE = "invoice";
  public static String ENTITY_BILL = "bill";
  public static String ENTITY_BILL_PAYMENT = "billpayment";

  public static CustomWorkflowConfig getConfig() throws Exception {

    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    return customWorkflowConfig;
  }

  public static CustomWorkflowConfig getConfig(String fileName) throws Exception {

    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(  MessageFormat.format("{0}/{1}", ROOT_CONFIG_PATH, fileName)),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    return customWorkflowConfig;

  }
}
