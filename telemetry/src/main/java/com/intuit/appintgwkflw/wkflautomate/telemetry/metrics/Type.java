package com.intuit.appintgwkflw.wkflautomate.telemetry.metrics;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum Type {

  APPLICATION_METRIC("APPLICATION"),
  API_METRIC("API"),
  SERVICE_METRIC("SERVICE"),
  EVENT_METRIC("EVENT"),
  APP_CONNECT_METRIC("APPCONNECT", WASContextEnums.HANDLER_ID),
  EXTERNAL_TASK_METRIC("EXTERNAL_TASK", WASContextEnums.TASK_TYPE),
  CAMUNDA_METRIC("CAMUNDA"),
  WAS_METRIC("WAS");

  private final String value;
  private final String lowerCaseValue;
  private final WASContextEnums[] mdcTags;

  Type(final String value, final WASContextEnums... mdcTags) {

    this.value = value;
    this.lowerCaseValue = value.toLowerCase();
    this.mdcTags = mdcTags;
  }
}
